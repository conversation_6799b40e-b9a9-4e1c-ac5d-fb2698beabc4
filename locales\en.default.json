{"general": {"call": "Call", "directions": "Directions", "health_pass_partner": "Healthpass partner", "back_to_homepage": "Back to Homepage", "version": "Version", "trusted_by": "Trusted by", "see_less": "See less", "see_more": "See more", "most_popular": "Most popular", "copyright": "Copyright", "styku_llc": "Styku LLC", "request_sent_title": "Request sent", "request_sent_description": "Someone from this location will be in touch soon to schedule your scan. Be sure to check your email.", "close_button_text": "Close", "filters_text": "Filters", "activeFilters": "Active filters", "near_me": "Near me", "input_search_placeholder": "Company name, city, state, address", "continue_to_checkout": "Continue to checkout", "customer": {"login": "Log in", "logout": "Log out", "profile": "My profile", "profile_info": "Profile Information", "membership": "Healthpass Member", "scan_package_bundle": "Preventive body scans available", "no_scans_available_message": "Find the nearest location and get a preventative body scan.", "healthpass_subscription": "Healthpass subscription", "annual_membership": "Annual Membership", "view_membership": "View Membership", "get_healthpass": "Get Healthpass", "healthpass_subscription_message": "Healthpass includes 4 preventive body scans and 2 free at-home blood tests. With a total savings of over $400.", "no_active_membership": "No active membership", "select_membership": "Select a membership", "cancel_membership": "Cancel membership", "more_details": "More details", "manage_subscription": "Manage subscription", "scans_available": "Scans available", "no_scans_available": "You have no scans", "member_since": "Member since", "already_have_active_subscription": "You already have an active Healthpass Membership", "subscription_will_renew_on": "Your subscription will renew on {{ date }}", "subscription_valid_until": "Your subscription is valid until {{ date }}", "at_home_diagnostics": "At-home diagnostics", "faq": "Frequently Asked Questions", "customer_membership_warning_messasge": {"title": "You already have an active Healthpass Membership.", "description_html": "Customers with active Healthpass Memberships may not purchase multiple memberships.", "action_message": "Please remove the Healthpass Membership from your cart to continue with checkout.", "link_message": "You can review your current membership here:"}, "forget_password": "Forgot password", "next": "Next", "submit": "Submit", "continue": "Continue", "not_get_email": "Didn’t get the email?", "resend_email": "Resend email", "dashboard": "Dashboard", "number_of_scans": "Number of Scans", "last_scan": "Last scan", "billing_and_payments": "Billing & Payments", "manage": "Manage", "change_email": "Change email", "change_password": "Change password", "policy_message_html": "I accept the <a class='text-sm text-primary-gradient' href='{{ terms_and_conditions_url }}' target='_blank'>terms and conditions</a> and <a class='text-sm text-primary-gradient' href='{{ privacy_policy_url }}' target='_blank'>privacy policy</a>."}, "pagination": {"label": "Pagination", "page": "Page {{ number }}", "next": "Next page", "previous": "Previous page"}, "success_message": {"verification_code_sent": "Verification code has been sent successfully", "password_changed_successfully": "Password changed successfully"}, "error_message": {"invalid_email": "Invalid email", "unable_to_send_email": "Unable to send an email", "invalid_verification_code": "Invalid verification code", "invalid_password": "Invalid password", "weak_password_missing_letter": "Weak password. Please include at least one letter", "old_password_not_matching_current": "Entered old password does not match the current password", "new_password_not_same_as_old": "The new password should not be the same as the old password", "user_not_found": "User is not found", "something_went_wrong": "Something went wrong"}, "no_search_result": {"title": "No results", "message": "No results match your search criteria, please try again."}, "filters_dropdown_text": {"facility_type": "All Facilities", "search_radius_all": "All"}, "social": {"links": {"facebook": "Facebook", "twitter": "Twitter", "instagram": "Instagram", "linkedin": "LinkedIn", "youtube": "YouTube"}}, "schedule_scan": {"title": "Schedule scan", "description": "Fill out the form below and someone from this location will reach out to schedule your scan.", "form": {"submit": "Send message", "privacy_message": "You agree to our friendly", "privacy_link": "privacy policy", "error": {"submitForm": "Failed to schedule scan"}}}}, "input": {"email": {"label": "Email", "placeholder": "Enter Your Email Address", "error_message": {"invalid_email": "Invalid email address", "required_email": "Email is required"}}, "password": {"label": "Password", "placeholder": "Enter Password"}, "email_verification": {"label": "Verification code", "placeholder": "Enter Verification Code"}, "first_name": {"label": "First name", "placeholder": "Enter First Name"}, "last_name": {"label": "Last name", "placeholder": "Enter Last Name"}, "new_password": {"label": "New password", "placeholder": "Enter Password", "error_message": {"title": "Password must meet the following requirements", "minimum_characters": "Minimum of 8 characters", "number": "Include at least 1 number (0-9)", "special_characters_message": "One special character", "upper_lowercase_message": "One uppercase & one lowercase letter"}}, "confirm_password": {"label": "Confirm password", "placeholder": "Enter Confirm Password", "message": {"valid_password": "Passwords match", "invalid_password": "Password does not match"}}, "current_password": {"label": "Current password", "placeholder": "Enter Current Password"}, "phone_number": {"label": "Phone number", "error_message": {"invalid_phone": "Invalid phone number"}}}, "product": {"add_to_cart": "Add to cart", "buy_now": "Buy Now", "purchase": "Purchase", "free": "Free", "save_24_per_scan": "Save {{ percentage }} Per <PERSON>an", "preventative_body_scan_message_extra_html": "{{ quantity }} Preventative Body Scans (<s>{{ price_per_scan }}</s>  <strong>{{ sale_price }}/scan</strong>)", "one_person_consultation": "1 In-person Consultation", "preventative_body_scan": "1 Preventative Body Scan", "free_blood_tests_html": "{{ quantity }} <strong>Free</strong> Blood Test (physician reviewed)", "free_blood_tests_extra_message": "{{ quantity }} Blood Test (regular price is {{ price_per_test }}/test if bought separately)", "sold_out": "Sold out", "quantity": "Quantity", "description": "Description", "not_supported": "Not Supported", "test_kit_badge": {"CLIA_and_CAP_certified_labs": "CLIA and CAP Certified Labs", "physician_reviewed_results": "Physician-Reviewed Results"}, "price": {"regular_price": "Regular price", "sale_price": "Sale price", "unit_price": "Unit price"}}, "cart": {"title": "<PERSON><PERSON>", "summary": "Summary", "nav_link_text": "<PERSON><PERSON>", "continue_shopping": "Continue shopping", "checkout": "Checkout", "remove": "Remove", "subtotal": "Subtotal", "total": "Total", "recurring_subtotal": "Recurring subtotal", "every_month": "every month", "every_year": "every year", "item_added_in_cart": "Item added to cart!", "free_product": {"preventative_body_scans_html": "{{ quantity }} Preventative Body Scans", "general_wellness_blood_tests_html": "{{ quantity }} General Wellness Blood Test"}, "empty": {"title": "Your cart is empty", "message": "Check our available scanning locations and select a plan that better suits your needs."}, "alert_message": {"age_restriction_html": "<strong>Age Restriction:</strong> At-home blood tests require that all test takers must be at least 18 years of age.", "notice_for_new_york_city_residents_html": "<strong>Notice for New York Residents:</strong> New York residents are not eligible for at-home blood tests due to state regulation of laboratory testing.", "policy_message_html": "More information can be found on Styku’s <a class='text-xs underline' href='{{ terms_and_conditions_url }}' target='_blank'>Terms and Conditions</a> and <a class='text-xs underline' href='{{ privacy_policy_url }}' target='_blank'>Privacy Policy</a>."}}, "customer": {"account": {"title": "Account", "details": "Account details", "view_addresses": "View addresses", "return": "Return to Account details"}, "account_fallback": "Account", "activate_account": {"title": "Activate account", "subtext": "Create your password to activate your account.", "password": "Password", "password_confirm": "Confirm password", "submit": "Activate account", "cancel": "Decline invitation"}, "addresses": {"title": "Addresses", "default": "<PERSON><PERSON><PERSON>", "add_new": "Add a new address", "edit_address": "Edit address", "first_name": "First name", "last_name": "Last name", "company": "Company", "address1": "Address 1", "address2": "Address 2", "city": "City", "country": "Country/region", "province": "Province", "zip": "Postal/ZIP code", "phone": "Phone", "set_default": "Set as default address", "add": "Add address", "update": "Update address", "cancel": "Cancel", "edit": "Edit", "delete": "Delete", "delete_confirm": "Are you sure you wish to delete this address?"}, "log_in": "Log in", "log_out": "Log out", "login_page": {"cancel": "Cancel", "create_account": "Create account", "email": "Email", "forgot_password": "Forgot your password?", "guest_continue": "Continue", "guest_title": "Continue as a guest", "password": "Password", "title": "<PERSON><PERSON>", "sign_in": "Sign in", "submit": "Submit", "alternate_provider_separator": "or"}, "order": {"title": "Order {{ name }}", "date_html": "Placed on {{ date }}", "cancelled_html": "Order Cancelled on {{ date }}", "cancelled_reason": "Reason: {{ reason }}", "billing_address": "Billing Address", "payment_status": "Payment Status", "shipping_address": "Shipping Address", "fulfillment_status": "Fulfillment Status", "discount": "Discount", "shipping": "Shipping", "tax": "Tax", "product": "Product", "sku": "SKU", "price": "Price", "quantity": "Quantity", "total": "Total", "total_refunded": "Refunded", "fulfilled_at_html": "Fulfilled {{ date }}", "track_shipment": "Track shipment", "tracking_url": "Tracking link", "tracking_company": "Carrier", "tracking_number": "Tracking number", "subtotal": "Subtotal", "total_duties": "Duties"}, "orders": {"title": "Order history", "order_number": "Order", "order_number_link": "Order number {{ number }}", "date": "Date", "payment_status": "Payment status", "fulfillment_status": "Fulfillment status", "total": "Total", "none": "You haven't placed any orders yet."}}, "vital": {"open_report": "Open report", "has_order": {"title": "Track your health with at-home blood tests", "button_text": "See all blood tests"}, "no_order": {"title": "No purchases made yet", "button_text": "Explore blood tests", "message": "You have not purchased any at-home blood tests. Learn more about the convenience of at-home diagnostics for preventative care by exploring all Styku at-home blood tests."}, "events": {"ordered": {"status": "Ordered", "message_html": "Hey, {{ customer_name }}, Your order for the {{ test_kit_name }} blood test has been placed! We'll text you updates as your order ships."}, "awaiting_registration": {"status": "Awaiting registration", "message_html": ""}, "cancelled": {"status": "Cancelled", "message_html": "Your order for {{ test_kit_name }} blood test has been cancelled. We hope to see you again soon."}, "requisition_created": {"status": "Requisition created", "message_html": "An order requisition form was validated and created with the partner laboratory, making the order available to be carried out."}, "registered": {"status": "Registration", "message_html": "Your {{ test_kit_name }} blood test has been registered. Be sure to watch the video and read through the pamphlet for instructions on how to collect your sample."}, "transit_customer": {"status": "In transit", "message_html": "Your order for the {{ test_kit_name }} blood test is in transit. You can track it's progress here: <a class='text-primary-gradient text-base' href='{{ tracking_link }}' target='_blank'>Track your order</a>"}, "out_for_delivery": {"status": "Out for delivery", "message_html": "Your order for the {{ test_kit_name }} blood test is out for delivery."}, "failure_to_deliver_to_customer": {"status": "Failed home delivery", "message_html": "We were unable to deliver your {{ test_kit_name }} blood test. Please reach out to customer support for further details."}, "with_customer": {"status": "With customer", "message_html": "Your {{ test_kit_name }} blood test has been delivered. Be sure to register your test kit in the Styku mobile app and follow the instructions provided."}, "transit_lab": {"status": "Transit to lab", "message_html": "Your {{ test_kit_name }} blood test is on it’s way to the lab. Once delivered, the lab will analyze your sample and you will receive your results in the Styku mobile app."}, "failure_to_deliver_to_lab": {"status": "Failed lab delivery", "message_html": "The shipping company was unable to deliver your {{ test_kit_name }} blood test to our labs. We are looking into this to resolve the issue. If you have any questions, please reach out to customer support."}, "delivered_to_lab": {"status": "Delivered to lab", "message_html": "Your {{ test_kit_name }} blood test has been delivered to one of our labs and your sample is being processed. We will let you know soon when your results are ready."}, "sample_error": {"status": "Sample error", "message_html": "There was an error with your collection sample. Please reach out to customer support to get another {{ test_kit_name }} blood test for no additional fees."}, "completed": {"status": "Completed", "message_html": "Your health results are in! The lab has finished processing your {{ test_kit_name }} blood test. You can review your results in the Styku mobile app."}}}, "sections": {"banner_with_video": {"title": "Get Your Whole Body Scan", "paragraph": "Healthpass provides access to help you better understand your health with a 35-second infrared light scan using artificial intelligence to measure hundreds of biomarkers and predict dozens of health outcomes.", "button_text": "Book Scan", "subtext": {"active_locations": "Active Locations", "body_scans_performed": "Body Scans Performed"}}, "case_study": {"button_text": "Read study"}, "locations": {"title": "Find a location to scan", "subtitle": "Select a location to scan at and get unlimited scanning at participating Healthpass locations.", "button": {"book_scan": "Book Scan", "inquire_now": "Inquire now", "purchase_scan": "Purchase scan", "schedule_scan": "Schedule Scan"}, "facility_type": "Facility type", "business_offerings": "Business offerings", "business_hours": "Business hours", "scanning_hours": "Scanning hours", "member_scan_price": "Member scan price", "non_member_scan_price": "Non-member scan price", "walk_ins_welcome": "Walk-ins welcome", "appointment_to_scan_required": "Appointment to scan required", "closed": "Closed", "open_24": "Open 24 Hours", "not_available": "Not available", "not_found": {"business_hours": "Business hours not available.", "scanner_hours": "Scanner business hours not available."}, "error_message": {"permission_denied": "Please enable location services to use this feature.", "position_unavailable": "We couldn't get your location. Please try again.", "timeout": "Getting your location took too long. Please try again.", "unknown_error": "An error occurred while getting your location. Please try again."}}, "pricing": {"title": "Pricing", "subtitle": "Purchase a Preventative Body Scan or Comprehensive Health Screen bundle.", "button": {"purchase_membership": "Purchase Membership", "purchase_bundle": "Purchase Bundle"}, "price_swapper_string": {"choose_billing_option": "Choose billing option", "billed": "billed at purchase", "saving": "You're saving"}}, "health_product": {"title": "At-Home Testing Solutions", "subtitle": "Convenient at-home testing solutions. Take control of your health.", "custom_tags_title": "What’s tested", "badge": "HSA & FSA Eligible"}, "healthpass_offerings": {"most_popular": "Only available at Healthpass locations", "additional_pricing_info": "Pricing varies by location. May require additional purchases. Does not include a Health Screen.", "scans_available_info": "*Styku predicts DEXA results with 97% accuracy. No separate DEXA required.", "standard_body_scan": "Everything in our Standard Body Scan", "preventative_body_scans": "Includes everything in our Standard 3D and Preventative Body Scans"}, "login": {"email_screen": {"title": "Sign in", "description": "Enter your email to log into your account."}, "email_screen_while_checkout": {"title": "Welcome to Styku!", "description": "To provide a safe and secure check-out process please review your order and enter your email to secure your account and purchase.", "returning_styku_users": "Returning Styku Users: Enter the same email you scanned with and we will find your account.", "new_styku_user": "New Styku User: If you are new to Styku, enter your email to create your account and get started."}, "password_screen": {"title": "Welcome back!", "description": "Enter your password to log into your account."}}, "email_verification": {"title": "Verify your email address", "description": "Let’s verify and secure your account. An email with a numeric verification code has been sent to your email address. Please enter the code below."}, "secure_account": {"title": "Welcome to Styku. Continue creating your account", "multiple_scans_existing_profile_title_html": "We found you! You’ve scanned <span class='scans-count'>{{ scans }}</span> times.", "single_scan_or_existing_profile_title_html": "We found you! You’ve scanned <span class='scans-count'>{{ scans }}</span> time.", "description": "Please verify and secure your account."}, "reset_password": {"title": "Create new password", "description": "Setting a password ensures privacy and security of your data."}, "forget_password": {"title": "Forgot password", "description": "An email with a numeric verification code has been sent to your email address. Please enter the code below."}, "404": {"title": "Page not found", "subtext": "404"}}}