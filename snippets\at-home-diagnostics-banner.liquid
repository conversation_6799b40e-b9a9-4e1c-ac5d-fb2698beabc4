{% comment %}
  Accepts:
    has_vital_orders: <PERSON><PERSON><PERSON> indicating if there are vital orders
  Usage:
    {% render 'at-home-diagnostics-accordion-header', has_vital_orders: has_vital_orders %}
{% endcomment %}

<div class="content-block grid grid-col">
  <div class="heading-block md:mb-6 {% if has_vital_orders %}order-last {%  else %}mb-6 {% endif %} md:order-first">
    <h3 class="heading-level-3">{{ 'general.customer.at_home_diagnostics' | t }}</h3>
  </div>
  <div class="see-block-test-container border border-gray-2 relative overflow-hidden rounded-2xl {% if has_vital_orders %}mb-6 md:mb-0 {% else %} min-h-[530px] {% endif %}">
    <div
      class="absolute left-0 h-full w-full z-[1] {% if has_vital_orders %}w-full {%  else %} md:w-[55%]{% endif %} bg-gradient-to-r from-white via-white to-white/0 {% unless has_vital_orders %} rotate-90 md:rotate-0{% endunless %}"
    ></div>
    <div class="image-block absolute right-0 {% if has_vital_orders %}w-[40%] top-16 md:-top-16 {%  else %}!-right-12 top-64 md:top-16 w-[125%] md:w-auto{% endif %}">
      <img
        src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/metabolic-syndrome-screen-banner.png?v=1731671990"
        loading="lazy"
        width="auto"
        height="auto"
      >
    </div>
    <div class="see-blood-test-block p-6 relative z-[1]">
      {% if has_vital_orders %}
        <h2 class="text-primary-gradient font-bold text-xl mb-3">
          {{ 'vital.has_order.title' | t }}
        </h2>
      {% else %}
        <h2 class="text-primary-gradient font-bold text-xl mb-2">
          {{ 'vital.no_order.title' | t }}
        </h2>
        <p class="text-base text-secondary mb-3">{{ 'vital.no_order.message' | t }}</p>
      {% endif %}
      <a
        href="/collections/blood-tests"
        class="button-primary-gradient text-center px-6 py-1.5 text-sm font-bold"
      >
        {% if has_vital_orders %}
          {{ 'vital.has_order.button_text' | t }}
        {% else %}
          {{ 'vital.no_order.button_text' | t }}
        {% endif %}
      </a>
    </div>
  </div>
</div>
