{% comment %}
  Accepts:
    - vital_order: {Object} The vital order object containing order details.
    - last_event: {Object} The last event associated with the order.
  Usage:
  {% render 'at-home-diagnostics-report', vital_order: vital_order, last_event: last_event %}
{% endcomment %}

{% liquid
  assign completed_status = 'completed'
  assign cancelled_status = 'cancelled'
%}

<div class="report-block flex flex-col md:flex-row md:items-center justify-between w-full p-4 md:p-6  border border-gray-2 rounded-lg gap-4 md:gap-3">
  <div class="flex gap-2">
    {% unless vital_order.status == cancelled_status %}
      <div class="icon-block">{{- 'icon-primary-outline-doc.svg' | inline_asset_content -}}</div>
    {% endunless %}
    <div class="title-block">
      <h2 class="text-secondary font-bold text-base">
        <span class="flex items-center">
          {{- vital_order.product_name -}}
        </span>
      </h2>
      <p class="text-sm text-secondary">
        {{ last_event.created_at | date: '%b %d %Y %H:%M' }}
      </p>
    </div>
  </div>
  {% if vital_order.status == completed_status %}
    <a
      href="{{ vital_order.report }}"
      target="_blank"
      class="button-primary-light text-center rounded-full py-1 px-2 leading-[0]"
    >
      <span class="text-primary-gradient text-sm font-bold">{{- 'vital.open_report' | t -}}</span>
    </a>
  {% else -%}
    <div class="button-primary-gradient-outline text-center rounded-full py-1 px-2 leading-[0]">
      <span class="text-primary-gradient text-sm">
        {{- 'vital.events.cancelled.status' | t -}}
      </span>
    </div>
  {% endif %}
</div>
