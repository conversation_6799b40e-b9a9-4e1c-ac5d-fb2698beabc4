{% comment %}
  Accepts:
    - defined_statuses_list: List of statuses to display in the accordion body.
    - all_existing_status: List of statuses that have been completed.
    - vital_order: Order object containing product and customer information.
    - customer: Customer object containing name information.
    - tracking_link: Link to track the order.
  Usage:
    {% render 'at-home-diagnostics-accordion-body', defined_statuses_list: defined_statuses_list, all_existing_status: all_existing_status, vital_order: vital_order, customer: customer, tracking_link: tracking_link %}
{% endcomment %}

<div class="border border-[#C8277D] border-t-0 rounded-b-xl">
  <div class="p-4 md:p-6 flex flex-col gap-6">
    {% liquid
      assign next_status_found = false
      assign disable_status = false
    %}
    {% for defined_status in defined_statuses_list %}
      <div class="flex gap-2">
        <div class="icon-block flex-none w-7">
          {%- if all_existing_status contains defined_status -%}
            {% render 'green-check-mark-icon', width: '24', height: '24' %}
          {% elsif next_status_found == false %}
            <img
              src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Group_482382.svg?v=1727170258"
              width="24"
              height="24"
            >
            {% assign next_status_found = true %}
          {% else %}
            {% assign disable_status = true %}
            <div class="border-gray-2 border-2 rounded-full size-6"></div>
          {% endif %}
        </div>
        <div
          class="text-block"
          {% if disable_status == true %}
            style="opacity: 0.3; user-select: none;"
          {% endif %}
        >
          <h3 class="text-base text-secondary font-bold">
            {{- 'vital.events' | append: '.' | append: defined_status | append: '.status' | t -}}
          </h3>
          {% if disable_status == false %}
            <p class="paragraph-text">
              {{-
                'vital.events'
                | append: '.'
                | append: defined_status
                | append: '.message_html'
                | t: test_kit_name: vital_order.product_name, customer_name: customer.name, tracking_link: tracking_link
              -}}
            </p>
          {% endif %}
        </div>
      </div>
    {% endfor %}
  </div>
</div>
