{% comment %}
  Accepts:
    - timeline_events: String of timeline events with icon types
    - has_failed_status: Boolean indicating if order has failed
    - failed_event: Failed event object if exists
    - vital_order: Order object containing product and customer information.
    - customer: Customer object containing name information.
    - tracking_link: Link to track the order.
  Usage:
    {% render 'at-home-diagnostics-accordion-body', timeline_events: timeline_events, has_failed_status: has_failed_status, failed_event: failed_event, vital_order: vital_order, customer: customer, tracking_link: tracking_link %}
{% endcomment %}

{% liquid
  assign timeline_events_array = timeline_events | split: ','
%}

<div class="border border-[#C8277D] border-t-0 rounded-b-xl">
  <div class="p-4 md:p-6 flex flex-col gap-6">
    {% comment %} Display timeline events {% endcomment %}
    {% for event_data in timeline_events_array %}
      {% liquid
        assign event_parts = event_data | split: '|'
        assign status = event_parts[0]
        assign icon_type = event_parts[1]
        assign status_key = event_parts[2]

        comment
        assign status_display = status | replace: '_', ' ' | capitalize
        if status == 'transit_customer'
          assign status_display = 'In transit'
        elsif status == 'out_for_delivery'
          assign status_display = 'Out for delivery'
        elsif status == 'with_customer'
          assign status_display = 'With customer'
        elsif status == 'transit_lab'
          assign status_display = 'Transit to lab'
        elsif status == 'delivered_to_lab'
          assign status_display = 'Delivered to lab'
        elsif status == 'awaiting_registration'
          assign status_display = 'Awaiting registration'
        elsif status == 'requisition_created'
          assign status_display = 'Requisition created'
        endif

        comment
        assign message = ''
        if icon_type == 'green' and status_key != ''
          assign message_key = 'vital.events.' | append: status | append: '.message_html'
          assign message = message_key | t: test_kit_name: vital_order.product_name, customer_name: customer.name, tracking_link: tracking_link
          if message == message_key
            assign message = 'Your ' | append: vital_order.product_name | append: ' order status has been updated to: ' | append: status_display
          endif
        elsif icon_type == 'red'
          assign message = 'Next step in your order process.'
        endif
      %}

      <div class="flex gap-2">
        <div class="icon-block flex-none w-7">
          {% if icon_type == 'green' %}
            {% render 'green-check-mark-icon', width: '24', height: '24' %}
          {% elsif icon_type == 'red' %}
            <img
              src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Group_482382.svg?v=1727170258"
              width="24"
              height="24"
            >
          {% else %}
            <div class="border-gray-2 border-2 rounded-full size-6"></div>
          {% endif %}
        </div>
        <div
          class="text-block"
          {% if icon_type == 'gray' %}
            style="opacity: 0.3; user-select: none;"
          {% endif %}
        >
          <h3 class="text-base text-secondary font-bold">
            {{ status_display }}
          </h3>
          {% if message != '' and icon_type != 'gray' %}
            <p class="paragraph-text">
              {{ message }}
            </p>
          {% endif %}
        </div>
      </div>
    {% endfor %}

    {% comment %} Show failure event if exists {% endcomment %}
    {% if has_failed_status and failed_event %}
      {% liquid
        assign failure_status = failed_event.status | split: '.' | last
        assign failure_display = failure_status | replace: '_', ' ' | capitalize
        if failure_status == 'sample_error'
          assign failure_display = 'Sample error'
        elsif failure_status == 'failure_to_deliver_to_customer'
          assign failure_display = 'Failed home delivery'
        elsif failure_status == 'failure_to_deliver_to_lab'
          assign failure_display = 'Failed lab delivery'
        elsif failure_status == 'problem_in_transit_customer'
          assign failure_display = 'Problem in transit'
        elsif failure_status == 'problem_in_transit_lab'
          assign failure_display = 'Problem in transit to lab'
        endif

        assign failure_message_key = 'vital.events.' | append: failure_status | append: '.message_html'
        assign failure_message = failure_message_key | t: test_kit_name: vital_order.product_name, customer_name: customer.name, tracking_link: tracking_link
        if failure_message == failure_message_key
          assign failure_message = 'There was an issue with your ' | append: vital_order.product_name | append: ' order. Please contact customer support for assistance.'
        endif
      %}

      <div class="flex gap-2">
        <div class="icon-block flex-none w-7">
          <div class="border-red-600 border-2 rounded-full size-6 bg-red-100 flex items-center justify-center">
            <span class="text-red-600 text-xs font-bold">!</span>
          </div>
        </div>
        <div class="text-block">
          <h3 class="text-base text-red-600 font-bold">
            {{ failure_display }}
          </h3>
          <p class="paragraph-text text-red-600">
            {{ failure_message }}
          </p>
        </div>
      </div>
    {% endif %}
  </div>
</div>
