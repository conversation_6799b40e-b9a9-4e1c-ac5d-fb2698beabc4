{% comment %}
  Accepts:
    vital_order: Vital order object
    last_event: Last event object related to the vital order
  Usage:
    {% render 'at-home-diagnostics-accordion-header', vital_order: vital_order, last_event: last_event %}
{% endcomment %}

<button
  type='button'
  class='flex flex-col md:flex-row md:items-center justify-between w-full p-4 md:p-6 font-bold text-secondary border border-gray-2 rounded-t-xl  gap-3 rounded-b-xl'
  data-accordion-target='#accordion-open-body-{{ vital_order.order_id }}'
  aria-expanded='false'
  aria-controls='accordion-open-body-{{ vital_order.order_id }}'
>
  <div class='text-left'>
    <span class='flex items-center text-base'>
      {{- vital_order.product_name -}}
    </span>
    <span class='flex items-center text-base'>{{ vital_order.sample_id }}</span>
    <p class='text-sm text-secondary font-normal'>
      {{- last_event.created_at | date: '%b %d %Y %H:%M' }}
    </p>
  </div>

  <div class='flex gap-2 justify-between w-full md:w-auto'>
    <div class='button-primary-gradient-outline text-center rounded-full py-1 px-2 leading-[0]'>
      <span class='text-primary-gradient text-sm font-normal'>
        {{- 'vital.events' | append: '.' | append: next_status | append: '.status' | t -}}
      </span>
    </div>
    {% render 'carret-icon', is_data_accordion_icon: true %}
  </div>
</button>
