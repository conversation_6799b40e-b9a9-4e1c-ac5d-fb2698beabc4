{% liquid
  assign completed_status = 'completed'
  assign failed = 'failed'
  assign failure_to_deliver_to_customer = 'failure_to_deliver_to_customer'
  assign failure_to_deliver_to_lab = 'failure_to_deliver_to_lab'
  assign problem_in_transit_customer = 'problem_in_transit_customer'
  assign problem_in_transit_lab = 'problem_in_transit_lab'
  assign sample_error = 'sample_error'
  assign cancelled_status = 'cancelled'
  assign statuses = 'ordered,transit_customer,out_for_delivery,with_customer,registered,transit_lab,delivered_to_lab,completed' | split: ','

  assign has_vital_orders = false
  for order in customer.orders
    if order.metafields.vital.vital_orders
      assign has_vital_orders = true
    endif
  endfor
%}

<div class="at-home-diagnostics space-y-6">
  <div class="content-block grid grid-col">
    <div class="heading-block md:mb-6 {% if has_vital_orders %}order-last {%  else %}mb-6 {% endif %} md:order-first">
      <h3 class="heading-level-3">{{ 'general.customer.at_home_diagnostics' | t }}</h3>
    </div>
    <div class="see-block-test-container border border-gray-2 relative overflow-hidden rounded-2xl {% if has_vital_orders %}mb-6 md:mb-0 {% else %} min-h-[530px] {% endif %}">
      <div
        class="absolute left-0 h-full w-full z-[1] {% if has_vital_orders %}w-full {%  else %} md:w-[55%]{% endif %} bg-gradient-to-r from-white via-white to-white/0 {% unless has_vital_orders %} rotate-90 md:rotate-0{% endunless %}"
      ></div>
      <div class="image-block absolute right-0 {% if has_vital_orders %}w-[40%] top-16 md:-top-16 {%  else %}!-right-12 top-64 md:top-16 w-[125%] md:w-auto{% endif %}">
        <img
          src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/metabolic-syndrome-screen-banner.png?v=1731671990"
          loading="lazy"
          width="auto"
          height="auto"
        >
      </div>
      <div class="see-blood-test-block p-6 relative z-[1]">
        {% if has_vital_orders %}
          <h2 class="text-primary-gradient font-bold text-xl mb-3">
            {{ 'vital.has_order.title' | t }}
          </h2>
        {% else %}
          <h2 class="text-primary-gradient font-bold text-xl mb-2">
            {{ 'vital.no_order.title' | t }}
          </h2>
          <p class="text-base text-secondary mb-3">{{ 'vital.no_order.message' | t }}</p>
        {% endif %}
        <a
          href="/collections/blood-tests"
          class="button-primary-gradient text-center px-6 py-1.5 text-sm font-bold"
        >
          {% if has_vital_orders %}
            {{ 'vital.has_order.button_text' | t }}
          {% else %}
            {{ 'vital.no_order.button_text' | t }}
          {% endif %}
        </a>
      </div>
    </div>
  </div>
  {% if has_vital_orders %}
    <div class="rounded-2xl md:border md:border-gray-2 md:p-6">
      <div
        class="grid grid-cols-1 gap-4 "
      >
        <div
          id="accordion-open"
          class="flex flex-col gap-6"
          data-accordion="open"
          data-active-classes="text-secondary bg-white !border-[#C8277D] border-b-0"
          data-inactive-classes="text-secondary bg-white rounded-b-xl"
        >
          {% for item in customer.orders %}
            {% for vital_order in item.metafields.vital.vital_orders.value %}
              {% assign vital_order_product_name = vital_order.product_name %}
              {% assign vital_order_status = vital_order.status %}
              {% assign last_event = vital_order.events.value | last %}
              {% if vital_order.status == completed_status or vital_order_status == cancelled_status %}
                <div class="report-block flex flex-col md:flex-row md:items-center justify-between w-full p-4 md:p-6  border border-gray-2 rounded-lg gap-4 md:gap-3">
                  <div class="flex gap-2">
                    {% unless vital_order_status == cancelled_status %}
                      <div class="icon-block">{{- 'icon-primary-outline-doc.svg' | inline_asset_content -}}</div>
                    {% endunless %}
                    <div class="title-block">
                      <h2
                        class="text-secondary font-bold text-base"
                      >
                        <span class="flex items-center">
                          {{- vital_order_product_name -}}
                        </span>
                      </h2>
                      <p class="text-sm text-secondary">
                        {{ last_event.created_at | date: '%b %d %Y %H:%M' }}
                      </p>
                    </div>
                  </div>
                  {% if vital_order.status == completed_status %}
                    <a
                      href="{{ vital_order.report }}"
                      target="_blank"
                      class="button-primary-light text-center rounded-full py-1 px-2 leading-[0]"
                    >
                      <span class="text-primary-gradient text-sm font-bold">{{- 'vital.open_report' | t -}}</span>
                    </a>
                  {% else -%}
                    <div class="button-primary-gradient-outline text-center rounded-full py-1 px-2 leading-[0]">
                      <span class="text-primary-gradient text-sm">
                        {{- 'vital.events.cancelled.status' | t -}}
                      </span>
                    </div>
                  {% endif %}
                </div>
              {% else %}
                <div class="accordion-group">
                  <h2 id="accordion-open-{{ vital_order.order_id }}">
                    <button
                      type="button"
                      class="flex flex-col md:flex-row md:items-center justify-between w-full p-4 md:p-6 font-bold text-secondary border border-gray-2 rounded-t-xl  gap-3 rounded-b-xl"
                      data-accordion-target="#accordion-open-body-{{ vital_order.order_id }}"
                      aria-expanded="false"
                      aria-controls="accordion-open-body-{{ vital_order.order_id }}"
                    >
                      <div class="text-left">
                        <span class="flex items-center text-base">
                          {{- vital_order_product_name -}}
                        </span>
                        <span class="flex items-center text-base">{{ vital_order.sample_id }}</span>
                        <p class="text-sm text-secondary font-normal">
                          {{- last_event.created_at | date: '%b %d %Y %H:%M' }}
                        </p>
                      </div>

                      {% liquid
                        assign all_status = vital_order.events.value | map: 'status' | append: ','
                        assign tracking_link = vital_order.outbound_tracking_url | default: '/'

                        assign next_status = ''
                        assign vital_order_status_failed = false
                        assign status_found = false

                        for status in statuses
                          if all_status contains status
                            continue
                            if vital_order_status == 'failed'
                              assign vital_order_status_failed = true
                              assign status_found = true
                            endif
                          elsif status_found == false
                            assign next_status = status
                            assign status_found = true
                          endif
                        endfor
                      %}

                      {{ vital_order_status }}
                      {{ vital_order_status_failed }}
                      <div class="flex gap-2 justify-between w-full md:w-auto">
                        <div class="button-primary-gradient-outline text-center rounded-full py-1 px-2 leading-[0]">
                          <span class="text-primary-gradient text-sm font-normal">
                            {{- 'vital.events' | append: '.' | append: next_status | append: '.status' | t -}}
                          </span>
                        </div>
                        {% render 'carret-icon', is_data_accordion_icon: true %}
                      </div>
                    </button>
                  </h2>
                  <div
                    id="accordion-open-body-{{ vital_order.order_id }}"
                    class="hidden"
                    aria-labelledby="accordion-open-{{ vital_order.order_id }}"
                  >
                    <div class="border border-[#C8277D] border-t-0 rounded-b-xl">
                      <div class="p-4 md:p-6 flex flex-col gap-6">
                        {% liquid
                          assign next_status_found = false
                          assign disable_status = false
                        %}
                        {% for defined_status in statuses %}
                          <div class="flex gap-2">
                            <div class="icon-block flex-none w-7">
                              {%- if all_status contains defined_status -%}
                                {% render 'green-check-mark-icon', width: '24', height: '24' %}
                              {% elsif next_status_found == false %}
                                <img
                                  src="https://cdn.shopify.com/s/files/1/0674/2579/6323/files/Group_482382.svg?v=1727170258"
                                  width="24"
                                  height="24"
                                >
                                {% assign next_status_found = true %}
                              {% else %}
                                {% assign disable_status = true %}
                                <div class="border-gray-2 border-2 rounded-full size-6"></div>
                              {% endif %}
                            </div>
                            <div
                              class="text-block"
                              {% if disable_status == true %}
                                style="opacity: 0.3; user-select: none;"
                              {% endif %}
                            >
                              <h3 class="text-base text-secondary font-bold">
                                {{- 'vital.events' | append: '.' | append: defined_status | append: '.status' | t -}}
                              </h3>
                              {% if disable_status == false %}
                                <p class="paragraph-text">
                                  {{-
                                    'vital.events'
                                    | append: '.'
                                    | append: defined_status
                                    | append: '.message_html'
                                    | t:
                                      test_kit_name: vital_order_product_name,
                                      customer_name: customer.name,
                                      tracking_link: tracking_link
                                  -}}
                                </p>
                              {% endif %}
                            </div>
                          </div>
                        {% endfor %}
                      </div>
                    </div>
                  </div>
                </div>
              {% endif %}
            {% endfor %}
          {% endfor %}
        </div>
      </div>
    </div>
  {% endif %}
</div>
