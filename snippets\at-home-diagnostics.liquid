{% liquid
  assign completed_status = 'completed'
  assign cancelled_status = 'cancelled'
  assign failed_status = 'failed'
  assign defined_statuses_list = 'ordered,transit_customer,out_for_delivery,with_customer,registered,transit_lab,delivered_to_lab,completed' | split: ','

  assign has_vital_orders = false
  for order in customer.orders
    if order.metafields.vital.vital_orders
      assign has_vital_orders = true
    endif
  endfor
%}
{% comment %} assign defined_statuses_list = 'ordered,transit_customer,out_for_delivery,problem_in_transit_customer,failure_to_deliver_to_customer,with_customer,registered,transit_lab,problem_in_transit_lab,failure_to_deliver_to_lab,delivered_to_lab,sample_error,completed' | split: ',' {% endcomment %}

<div class="at-home-diagnostics space-y-6">
  {% render 'at-home-diagnostics-banner', has_vital_orders: has_vital_orders %}
  {% if has_vital_orders %}
    <div class="rounded-2xl md:border md:border-gray-2 md:p-6">
      <div
        class="grid grid-cols-1 gap-4 "
      >
        <div
          id="accordion-open"
          class="flex flex-col gap-6"
          data-accordion="open"
          data-active-classes="text-secondary bg-white !border-[#C8277D] border-b-0"
          data-inactive-classes="text-secondary bg-white rounded-b-xl"
        >
          {% for item in customer.orders %}
            {% for vital_order in item.metafields.vital.vital_orders.value %}
              {% liquid
                assign all_existing_status = vital_order.events.value | map: 'status'
                assign last_event_status = vital_order.events.value | last
                assign tracking_link = vital_order.outbound_tracking_url | default: '/'

                comment
                assign has_failed_status = false
                assign failed_event = null
                for event in vital_order.events.value
                  if event.status contains 'failed.'
                    assign has_failed_status = true
                    assign failed_event = event
                    break
                  endif
                endfor

                comment
                assign timeline_events = ''
                assign next_event_found = false

                for status in defined_statuses_list
                  assign status_completed = false
                  assign status_key = ''

                  comment
                  for existing_status in all_existing_status
                    assign existing_status_key = existing_status | split: '.' | last
                    if existing_status_key == status
                      assign status_completed = true
                      assign status_key = existing_status
                      break
                    endif
                  endfor

                  comment
                  assign icon_type = 'gray'
                  if status_completed
                    assign icon_type = 'green'
                  elsif next_event_found == false and has_failed_status == false
                    assign icon_type = 'red'
                    assign next_event_found = true
                  endif

                  comment
                  assign event_data = status | append: '|' | append: icon_type | append: '|' | append: status_key
                  if timeline_events == ''
                    assign timeline_events = event_data
                  else
                    assign timeline_events = timeline_events | append: ',' | append: event_data
                  endif

                  comment
                  if has_failed_status and status_completed
                    break
                  endif
                endfor
              %}

              {% if vital_order.status == completed_status or vital_order.status == cancelled_status %}
                {% render 'at-home-diagnostics-report', vital_order: vital_order, last_event: last_event_status %}
              {% else %}
                <div class="accordion-group">
                  <div id="accordion-open-{{ vital_order.order_id }}">
                    {% render 'at-home-diagnostics-accordion-header',
                      vital_order: vital_order,
                      last_event: last_event_status,
                      has_failed_status: has_failed_status,
                      failed_event: failed_event
                    %}
                  </div>
                  <div
                    id="accordion-open-body-{{ vital_order.order_id }}"
                    class="hidden"
                    aria-labelledby="accordion-open-{{ vital_order.order_id }}"
                  >
                    {% render 'at-home-diagnostics-accordion-body',
                      timeline_events: timeline_events,
                      has_failed_status: has_failed_status,
                      failed_event: failed_event,
                      vital_order: vital_order,
                      customer: customer,
                      tracking_link: tracking_link
                    %}
                  </div>
                </div>
              {% endif %}
            {% endfor %}
          {% endfor %}
        </div>
      </div>
    </div>
  {% endif %}
</div>
