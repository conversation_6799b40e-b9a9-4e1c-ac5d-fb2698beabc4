{% liquid
  assign completed_status = 'completed'
  assign cancelled_status = 'cancelled'
  assign failed_status = 'failed'
  assign defined_statuses_list = 'ordered,transit_customer,out_for_delivery,with_customer,registered,transit_lab,delivered_to_lab,completed' | split: ','

  assign has_vital_orders = false
  for order in customer.orders
    if order.metafields.vital.vital_orders
      assign has_vital_orders = true
    endif
  endfor
%}
{% comment %} assign defined_statuses_list = 'ordered,transit_customer,out_for_delivery,problem_in_transit_customer,failure_to_deliver_to_customer,with_customer,registered,transit_lab,problem_in_transit_lab,failure_to_deliver_to_lab,delivered_to_lab,sample_error,completed' | split: ',' {% endcomment %}

<div class="at-home-diagnostics space-y-6">
  {% render 'at-home-diagnostics-banner', has_vital_orders: has_vital_orders %}
  {% if has_vital_orders %}
    <div class="rounded-2xl md:border md:border-gray-2 md:p-6">
      <div
        class="grid grid-cols-1 gap-4 "
      >
        <div
          id="accordion-open"
          class="flex flex-col gap-6"
          data-accordion="open"
          data-active-classes="text-secondary bg-white !border-[#C8277D] border-b-0"
          data-inactive-classes="text-secondary bg-white rounded-b-xl"
        >
          {% for item in customer.orders %}
            {% for vital_order in item.metafields.vital.vital_orders.value %}
              {% liquid
                assign all_existing_status = vital_order.events.value | map: 'status' | append: ','
                assign last_event_status = vital_order.events.value | last
                assign tracking_link = vital_order.outbound_tracking_url | default: '/'
              %}

              {% if vital_order.status == completed_status or vital_order.status == cancelled_status %}
                {% render 'at-home-diagnostics-report', vital_order: vital_order, last_event: last_event_status %}
              {% else %}
                {% liquid
                  assign next_status = ''
                  assign status_found = false
                  assign failed_status_found = false
                  for status in statuses
                    if all_status contains status
                      continue
                    elsif status_found == false
                      assign next_status = status
                      assign status_found = true
                    endif
                  endfor
                %}

                <div class="accordion-group">
                  <div id="accordion-open-{{ vital_order.order_id }}">
                    {% render 'at-home-diagnostics-accordion-header',
                      vital_order: vital_order,
                      last_event: last_event_status,
                      next_status: next_status
                    %}
                  </div>
                  <div
                    id="accordion-open-body-{{ vital_order.order_id }}"
                    class="hidden"
                    aria-labelledby="accordion-open-{{ vital_order.order_id }}"
                  >
                    {% render 'at-home-diagnostics-accordion-body',
                      defined_statuses_list: defined_statuses_list,
                      all_existing_status: all_existing_status,
                      vital_order: vital_order,
                      customer: customer,
                      tracking_link: tracking_link
                    %}
                  </div>
                </div>
              {% endif %}
            {% endfor %}
          {% endfor %}
        </div>
      </div>
    </div>
  {% endif %}
</div>
